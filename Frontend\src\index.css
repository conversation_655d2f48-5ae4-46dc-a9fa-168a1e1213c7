@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Pinyon Script';
  src: url('./fonts/PinyonScript-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
body {
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom styles for select dropdowns with scrolling */
/* Only apply to selects with the scrollable-select class */
select.scrollable-select {
  /* Ensure dropdown options are visible and scrollable */
  appearance: auto;
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
}

/* For browsers that support it, limit dropdown height */
select.scrollable-select option {
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.4;
}

/* Custom scrollable select styling */
.scrollable-select {
  max-height: 200px;
  overflow-y: auto;
}
