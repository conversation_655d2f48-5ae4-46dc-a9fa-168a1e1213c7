{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cross-env NODE_ENV=development nodemon server.js", "start": "cross-env NODE_ENV=production node server.js"}, "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.4.5", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "helmet": "^8.0.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "sharp": "^0.33.5", "slugify": "^1.6.6", "validator": "^13.12.0", "xss-clean": "^0.1.4"}, "devDependencies": {"cross-env": "^7.0.3", "morgan": "^1.10.0", "nodemon": "^2.0.22"}}