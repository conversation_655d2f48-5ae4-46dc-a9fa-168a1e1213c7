{"name": "inventory-management-app", "version": "0.1.0", "private": true, "dependencies": {"@headlessui/react": "^1.7.13", "@heroicons/react": "^2.0.16", "@reduxjs/toolkit": "^2.2.7", "@tailwindcss/forms": "^0.5.3", "@tanstack/react-table": "^8.20.5", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "apexcharts": "^4.3.0", "axios": "^1.7.7", "chart.js": "^4.2.1", "lucide-react": "^0.452.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.5", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-modal": "^3.16.1", "react-redux": "^9.1.2", "react-router-dom": "^6.9.0", "react-select": "^5.10.1", "react-table": "^7.8.0", "react-to-print": "^3.0.5", "react-toastify": "^10.0.6", "tailwind-scrollbar": "^3.1.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.2.7", "vite": "^5.2.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}